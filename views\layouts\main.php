<!DOCTYPE html>
<html lang="id" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= isset($title) ? $title . ' - ' . SITE_NAME : SITE_NAME ?></title>
    <meta name="description" content="<?= isset($description) ? $description : SITE_DESCRIPTION ?>">
    <meta name="keywords" content="<?= META_KEYWORDS ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script type="text/javascript" id="tailwind-config">
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                            950: '#082f49',
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                            950: '#020617',
                        },
                        dark: {
                            50: '#fafafa',
                            100: '#f4f4f5',
                            200: '#e4e4e7',
                            300: '#d4d4d8',
                            400: '#a1a1aa',
                            500: '#71717a',
                            600: '#52525b',
                            700: '#3f3f46',
                            800: '#27272a',
                            900: '#18181b',
                            950: '#09090b',
                        },
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                        serif: ['Playfair Display', 'serif'],
                    },
                    spacing: {
                        '18': '4.5rem',
                        '88': '22rem',
                        '128': '32rem',
                    },
                    textShadow: {
                        DEFAULT: '0 2px 10px rgba(0, 0, 0, 0.8)',
                        sm: '0 1px 2px rgba(0, 0, 0, 0.7)',
                        md: '0 3px 6px rgba(0, 0, 0, 0.7)',
                        lg: '0 5px 15px rgba(0, 0, 0, 0.7)',
                        xl: '0 8px 25px rgba(0, 0, 0, 0.8)',
                    },
                    backdropBlur: {
                        xs: '2px',
                    },
                    animation: {
                        'fadeIn': 'fadeIn 0.5s ease-out forwards',
                        'fadeOut': 'fadeOut 0.5s ease-out forwards',
                        'slideUp': 'slideUp 0.6s ease-out forwards',
                        'slideDown': 'slideDown 0.6s ease-out forwards',
                        'scaleIn': 'scaleIn 0.4s ease-out forwards',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        fadeOut: {
                            '0%': { opacity: '1' },
                            '100%': { opacity: '0' },
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' },
                        },
                        slideDown: {
                            '0%': { opacity: '0', transform: 'translateY(-20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' },
                        },
                        scaleIn: {
                            '0%': { opacity: '0', transform: 'scale(0.9)' },
                            '100%': { opacity: '1', transform: 'scale(1)' },
                        }
                    },
                    minHeight: {
                        'screen-75': '75vh',
                        'screen-90': '90vh',
                    },
                    maxWidth: {
                        '8xl': '88rem',
                        '9xl': '96rem',
                    }
                }
            },
            plugins: [
                function({ addUtilities, theme, e }) {
                    const textShadowUtilities = Object.entries(theme('textShadow', {})).map(([key, value]) => {
                        return {
                            [`.${e(key === 'DEFAULT' ? 'text-shadow' : `text-shadow-${key}`)}`]: {
                                textShadow: value,
                            },
                        }
                    });
                    addUtilities(textShadowUtilities, ['responsive', 'hover']);

                    // Add custom utilities for better UX
                    addUtilities({
                        '.focus-ring': {
                            '@apply focus:outline-none focus:ring-4 focus:ring-primary-400/50': {},
                        },
                        '.btn-primary': {
                            '@apply bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-black font-bold py-4 px-8 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-2xl focus-ring': {},
                        },
                        '.btn-secondary': {
                            '@apply bg-black/60 hover:bg-black/80 border-2 border-gray-600 hover:border-primary-400 text-white font-bold py-4 px-8 rounded-xl transition-all duration-300 hover:scale-105 backdrop-blur-md focus-ring': {},
                        },
                        '.glass-card': {
                            '@apply bg-black/80 backdrop-blur-md border border-white/10 hover:border-primary-400/30 transition-all duration-300': {},
                        }
                    }, ['responsive', 'hover']);
                }
            ]
        }
    </script>
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom CSS -->
    
</head>
    <body class="font-sans text-white bg-black">
    <!-- Header/Navbar -->
    <header class="fixed w-full bg-black shadow-lg z-50 transition-all duration-300 ease-in-out">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-3">
                <!-- Logo -->
                <a href="<?= url('/') ?>" class="block group">
                    <img src="assets/images/4.webp" alt="Antosa Architect Logo" class="h-10 transition-transform duration-300 group-hover:scale-105">
                    <div class="h-[3px] bg-primary-400 mt-[2px] w-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 transform scale-x-0 group-hover:scale-x-100 origin-left"></div>
                </a>
                
                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-5 lg:space-x-7 mx-auto">
                    <a href="#home" class="nav-item text-sm font-medium text-primary-400 relative py-2 group active:text-primary-400 aria-current:text-primary-400">
                        Beranda
                        <span class="absolute bottom-0 left-0 w-full h-[2px] bg-primary-400 transform scale-x-100 transition-transform duration-300 ease-out group-active:bg-primary-400 group-[aria-current]:bg-primary-400"></span>
                    </a>
                    <a href="#about" class="nav-item text-sm font-medium text-dark-300 hover:text-primary-400 transition-colors relative py-2 group active:text-primary-400 aria-current:text-primary-400">
                        Tentang Kami
                        <span class="absolute bottom-0 left-0 w-full h-[2px] bg-primary-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out origin-center group-active:bg-primary-400 group-[aria-current]:bg-primary-400"></span>
                    </a>
                    <a href="#services" class="nav-item text-sm font-medium text-dark-300 hover:text-primary-400 transition-colors relative py-2 group active:text-primary-400 aria-current:text-primary-400">
                        Layanan
                        <span class="absolute bottom-0 left-0 w-full h-[2px] bg-primary-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out origin-center group-active:bg-primary-400 group-[aria-current]:bg-primary-400"></span>
                    </a>
                    <a href="#portfolio" class="nav-item text-sm font-medium text-dark-300 hover:text-primary-400 transition-colors relative py-2 group active:text-primary-400 aria-current:text-primary-400">
                        Portfolio
                        <span class="absolute bottom-0 left-0 w-full h-[2px] bg-primary-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out origin-center group-active:bg-primary-400 group-[aria-current]:bg-primary-400"></span>
                    </a>
                    <a href="#testimonials" class="nav-item text-sm font-medium text-dark-300 hover:text-primary-400 transition-colors relative py-2 group active:text-primary-400 aria-current:text-primary-400">
                        Testimonial
                        <span class="absolute bottom-0 left-0 w-full h-[2px] bg-primary-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out origin-center group-active:bg-primary-400 group-[aria-current]:bg-primary-400"></span>
                    </a>
                    <a href="#faq" class="nav-item text-sm font-medium text-dark-300 hover:text-primary-400 transition-colors relative py-2 group active:text-primary-400 aria-current:text-primary-400">
                        FAQ
                        <span class="absolute bottom-0 left-0 w-full h-[2px] bg-primary-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out origin-center group-active:bg-primary-400 group-[aria-current]:bg-primary-400"></span>
                    </a>
                    <a href="#contact" class="nav-item text-sm font-medium text-dark-300 hover:text-primary-400 transition-colors relative py-2 group active:text-primary-400 aria-current:text-primary-400">
                        Kontak
                        <span class="absolute bottom-0 left-0 w-full h-[2px] bg-primary-400 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 ease-out origin-center group-active:bg-primary-400 group-[aria-current]:bg-primary-400"></span>
                    </a>
                </nav>


                
                <!-- Mobile menu button -->
                <button id="mobile-menu-button" class="md:hidden flex items-center p-2 text-dark-300 hover:text-emerald-400 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-emerald-500 rounded-md">
                    <span class="sr-only">Open main menu</span>
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m4 6H4"></path> <!-- Adjusted icon for clarity -->
                    </svg>
                </button>
            </div>
            
            <!-- Mobile Navigation -->
            <div id="mobile-menu" class="md:hidden hidden bg-black">
                <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                    <a href="#home" class="mobile-nav-item block px-3 py-2 rounded-md text-base font-medium text-emerald-400 hover:bg-emerald-900">Beranda</a>
                    <a href="#about" class="mobile-nav-item block px-3 py-2 rounded-md text-base font-medium text-white hover:text-emerald-400 hover:bg-emerald-900">Tentang Kami</a>
                    <a href="#services" class="mobile-nav-item block px-3 py-2 rounded-md text-base font-medium text-white hover:text-emerald-400 hover:bg-emerald-900">Layanan</a>
                    <a href="#portfolio" class="mobile-nav-item block px-3 py-2 rounded-md text-base font-medium text-dark-300 hover:text-emerald-400 hover:bg-dark-800">Portfolio</a>
                    <a href="#testimonials" class="mobile-nav-item block px-3 py-2 rounded-md text-base font-medium text-dark-300 hover:text-emerald-400 hover:bg-dark-800">Testimonial</a>
                    <a href="#faq" class="mobile-nav-item block px-3 py-2 rounded-md text-base font-medium text-dark-300 hover:text-emerald-400 hover:bg-dark-800">FAQ</a>
                    <a href="#contact" class="mobile-nav-item block px-3 py-2 rounded-md text-base font-medium text-dark-300 hover:text-emerald-400 hover:bg-dark-800">Kontak</a>
                </div>

            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main>
        <?= $content ?? '' ?>
    </main>

    <!-- JavaScript -->
    <script src="/assets/js/main.js" defer></script>
    <script src="/assets/js/content-toggle.js" defer></script>
    <script src="/assets/js/hero-slider.js" defer></script>
    <script src="/assets/js/home-portfolio.js" defer></script>
    <script src="/assets/js/faq.js" defer></script>

    <?php require_once 'partials/footer.php'; ?>
</body>
</html>
